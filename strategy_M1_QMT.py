#coding:gbk

# 导入所需的库
import pandas as pd
import numpy as np
import datetime
import math
import talib

# 全局变量类，用于保存策略状态
class StrategyContext:
    def __init__(self):
        # 设置交易标的
        self.stock_list = ['513100.SH',  # 纳斯达克ETF
                          '510300.SH',  # 沪深300ETF
                          '518880.SH']  # 黄金ETF
        
        # 设置技术指标参数
        self.bias_period = 24  # BIAS周期
        self.adx_period = 14   # ADX周期
        self.atr_period = 12   # ATR周期
        
        # 添加动量策略参数
        self.momentum_boost_factor = 0.06  # 动量增强系数
        self.max_momentum_allocation_pct = 0.199    # 最大动量分配比例
        self.momentum_threshold = 0.03    # 动量信号阈值
        self.momentum_M1_lookback = 5     # 定义过去1个月的回溯期 (约21个交易日)
        
        # 添加缓存字典
        self.cache = {
            'technical_indicators': {},
            'last_update_date': None,
            'price_data': {},
            'last_minute': -1,
            'last_money_fund_trade_date': None,
            'last_volatility_calc': {},  # 新增：缓存波动率计算
            'last_trend_calc': {},       # 新增：缓存趋势计算
            'last_rebalance_check': None, # 新增：缓存再平衡检查
            'last_momentum_check': None   # 新增：缓存动量检查
        }
        
        # 初始化市场状态字典
        self.market_states = {security: 'oscillating' for security in self.stock_list}
        
        # 初始化初始买入标志
        self.initial_buy_needed = True
        
        # 设置参数 (更新自 JSON)
        self.total_position_pct = 0.891  # 总仓位
        self.reserve_position_pct = 1 - self.total_position_pct  # 备用资金
        
        # 设置BIAS阈值
        self.bias_threshold = {
            '513100.SH': 0.1,   # 纳斯达克ETF阈值±10%
            '510300.SH': 0.1,  # 沪深300ETF阈值±10%
            '518880.SH': 0.1   # 黄金ETF阈值±10%
        }
        
        # 设置风控参数 (更新自 JSON)
        self.single_stock_stop_loss = 0.105  # 单个标的止损
        self.portfolio_stop_loss = 0.06  # 组合止损
        
        # 设置因子权重 (更新自 JSON)
        self.weight_trend = 0.302
        self.weight_direction = 0.498
        self.weight_volatility = 0.056
        self.weight_volume = 0.058
        self.weight_bias = 0.086
        
        # 设置趋势调整参数 (更新自 JSON)
        self.uptrend_base_extra = 0.198
        self.downtrend_base_reduction = 0.193
        self.downtrend_pct_limit = 0.309
        
        # 设置调整系数 (更新自 JSON)
        self.reserve_scale_down_factor = 0.5
        self.reserve_scale_up_factor = 1.25
        self.strong_trend_boost = 1.53
        self.oversold_reduction_factor = 0.930
        self.momentum_strength_factor_multiplier = 1.95
        
        # BIAS震荡判断放大系数
        self.bias_oscillation_scale_factor = 1.445
        
        # 动态配置: 趋势持续性计算的ADX基准
        self.alloc_persistence_adx_base = 35
        
        # 置信度计算参数
        self.conf_trend_base = 40
        self.conf_trend_weight = 0.4
        self.conf_bias_mult = 2
        self.conf_bias_weight = 0.3
        self.conf_adx_base = 40
        self.conf_adx_weight = 0.3
        self.conf_vol_offset = 0.5
        self.conf_vol_weight = 0.2
        
        # 运行时需要的变量
        self.last_rebalance_date = None  # 上次再平衡时间
        self.stop_trading = False  # 是否停止交易的标志
        
        # 添加货币基金相关参数
        self.money_fund = '511990.SH'  # 华宝添益货币ETF
        self.min_fund_amount = 1  # 最小货基交易金额（1元）
        self.money_fund_threshold = 0.01  # 货基交易阈值（1%）
        
        # 设置最小交易数量
        self.min_trade_amount = 100
        
        # 设置是否已经完成初始建仓的标志
        self.initial_position_established = False
        
        # QMT特有参数
        self.account_id = "strategy_M1"  # 账户ID
        self.last_bar_time = None  # 上次处理的K线时间
        self.last_market_open_time = None  # 上次开盘处理时间
        self.last_stop_loss_time = None  # 上次止损检查时间
        self.last_money_fund_time = None  # 上次货基交易时间
        self.last_fund_income_time = None  # 上次收益检查时间
        self.last_performance_time = None  # 上次性能监控时间
        self.last_momentum_time = None  # 上次动量调整时间

# 创建全局策略上下文
g = StrategyContext()

def init(C):
    """初始化函数"""
    print("=" * 60)
    print("策略初始化开始...")
    print("=" * 60)

    try:
        # 设置账户信息
        print("步骤1: 设置账户信息")
        g.account_id = C.accountid if hasattr(C, 'accountid') else "strategy_M1"
        print(f"账户ID设置完成: {g.account_id}")

        print("步骤2: 开始下载历史数据...")
        # 下载历史数据
        download_historical_data(C)
        print("历史数据下载完成")

        print("步骤3: 预加载历史数据...")
        # 预加载历史数据
        preload_historical_data(C)
        print("历史数据预加载完成")

        print("步骤4: 初始化完成检查")
        print(f"交易标的: {g.stock_list}")
        print(f"账户ID: {g.account_id}")
        print(f"总仓位: {g.total_position_pct:.1%}, 备用资金: {g.reserve_position_pct:.1%}")

        print("=" * 60)
        print("策略初始化成功完成!")
        print("=" * 60)

    except Exception as e:
        print("=" * 60)
        print(f"策略初始化失败: {str(e)}")
        print("=" * 60)
        import traceback
        traceback.print_exc()
        raise e

def download_historical_data(C):
    """下载历史数据"""
    try:
        print("开始下载策略所需的历史数据...")

        # 需要下载的数据周期
        periods = ['1d', '1m', '5m']

        # 计算需要下载的历史数据长度
        # 考虑到技术指标计算需要，下载更多历史数据
        max_period = max(60, g.bias_period, g.adx_period*2, g.atr_period*2, g.momentum_M1_lookback)
        download_count = max_period + 50  # 额外增加50天作为缓冲

        # 计算开始日期（大约需要的交易日数量）
        import datetime
        start_date = datetime.datetime.now() - datetime.timedelta(days=download_count * 2)  # 乘以2考虑非交易日
        start_date_str = start_date.strftime('%Y%m%d')

        total_downloads = len(g.stock_list) * len(periods)
        current_download = 0

        # 下载每个标的的历史数据
        for security in g.stock_list:
            for period in periods:
                current_download += 1
                try:
                    print(f"下载进度 {current_download}/{total_downloads}: {security} - {period}")

                    # 使用QMT的下载历史数据函数
                    download_history_data(security, period, start_date_str, '')

                    # 短暂延迟避免请求过快
                    import time
                    time.sleep(0.1)

                except Exception as e:
                    print(f"下载 {security} {period} 数据时出错: {str(e)}")
                    continue

        # 下载货币基金数据
        try:
            print(f"下载货币基金数据: {g.money_fund}")
            for period in ['1d']:  # 货基只需要日线数据
                download_history_data(g.money_fund, period, start_date_str, '')
        except Exception as e:
            print(f"下载货币基金数据时出错: {str(e)}")

        print("历史数据下载完成!")

    except Exception as e:
        print(f"下载历史数据时发生错误: {str(e)}")

def handlebar(C):
    """主要的策略执行函数"""
    try:
        print(f"\n{'='*50}")
        print(f"handlebar开始执行")
        print(f"{'='*50}")

        # 检查是否为最新K线
        print("步骤1: 检查K线状态")
        is_last = C.is_last_bar()
        print(f"是否为最新K线: {is_last}")

        # 跳过历史K线，只处理最新K线
        if not is_last:
            print("跳过历史K线，退出handlebar")
            return

        # 获取当前时间
        print("步骤2: 获取当前时间")
        current_time = datetime.datetime.now()
        current_date = current_time.date()
        current_time_str = current_time.strftime('%H%M%S')
        print(f"当前时间: {current_time}")
        print(f"当前日期: {current_date}")
        print(f"时间字符串: {current_time_str}")

        # 跳过非交易时间
        print("步骤3: 检查交易时间")
        if current_time_str < '093000' or current_time_str > '150000':
            print(f"非交易时间({current_time_str})，跳过执行")
            return
        print("在交易时间内，继续执行")

        # 防止重复执行
        print("步骤4: 检查重复执行")
        if g.last_bar_time == current_time:
            print("防止重复执行，跳过")
            return
        g.last_bar_time = current_time
        print("通过重复执行检查")

        # 更新技术指标
        print("步骤5: 更新技术指标")
        for i, security in enumerate(g.stock_list):
            print(f"更新技术指标 {i+1}/{len(g.stock_list)}: {security}")
            try:
                result = update_technical_indicators(C, security)
                print(f"  {security} 技术指标更新成功，返回类型: {type(result)}")
                if isinstance(result, dict):
                    print(f"  {security} 指标键: {list(result.keys())}")
            except Exception as e:
                print(f"  {security} 技术指标更新失败: {str(e)}")
                import traceback
                traceback.print_exc()
        print("技术指标更新完成")

        # 开盘时执行的任务 (9:30)
        print("步骤6: 检查开盘任务")
        if (current_time_str >= '093000' and current_time_str <= '093100' and
            g.last_market_open_time != current_date):
            print("执行开盘任务")
            try:
                market_open(C)
                g.last_market_open_time = current_date
                print("开盘任务执行完成")
            except Exception as e:
                print(f"开盘任务执行失败: {str(e)}")
        else:
            print("不在开盘任务时间窗口")

        # 止损检查 (9:31)
        print("步骤7: 检查止损任务")
        if (current_time_str >= '093100' and current_time_str <= '093200' and
            g.last_stop_loss_time != current_date):
            print("执行止损检查")
            try:
                check_stop_loss(C)
                g.last_stop_loss_time = current_date
                print("止损检查完成")
            except Exception as e:
                print(f"止损检查失败: {str(e)}")
        else:
            print("不在止损检查时间窗口")

        # 货基交易 (9:32)
        print("步骤8: 检查货基交易")
        if (current_time_str >= '093200' and current_time_str <= '093300' and
            g.last_money_fund_time != current_date):
            print("执行货基交易")
            try:
                trade_money_fund(C)
                g.last_money_fund_time = current_date
                print("货基交易完成")
            except Exception as e:
                print(f"货基交易失败: {str(e)}")
        else:
            print("不在货基交易时间窗口")

        # 收益检查 (15:10)
        print("步骤9: 检查收益检查")
        if (current_time_str >= '151000' and current_time_str <= '151100' and
            g.last_fund_income_time != current_date):
            print("执行收益检查")
            try:
                check_fund_income(C)
                g.last_fund_income_time = current_date
                print("收益检查完成")
            except Exception as e:
                print(f"收益检查失败: {str(e)}")
        else:
            print("不在收益检查时间窗口")

        # 性能监控 (15:00)
        print("步骤10: 检查性能监控")
        if (current_time_str >= '150000' and current_time_str <= '150100' and
            g.last_performance_time != current_date):
            print("执行性能监控")
            try:
                monitor_strategy_performance(C)
                g.last_performance_time = current_date
                print("性能监控完成")
            except Exception as e:
                print(f"性能监控失败: {str(e)}")
        else:
            print("不在性能监控时间窗口")

        # 每周一动量调整 (9:35)
        print("步骤11: 检查动量调整")
        if (current_time.weekday() == 0 and current_time_str >= '093500' and
            current_time_str <= '093600' and g.last_momentum_time != current_date):
            print("执行动量调整")
            try:
                apply_momentum_overlay(C)
                g.last_momentum_time = current_date
                print("动量调整完成")
            except Exception as e:
                print(f"动量调整失败: {str(e)}")
        else:
            print("不在动量调整时间窗口")

        print(f"{'='*50}")
        print(f"handlebar执行完成")
        print(f"{'='*50}\n")

    except Exception as e:
        print(f"{'='*50}")
        print(f"handlebar执行出错: {str(e)}")
        print(f"{'='*50}")
        import traceback
        traceback.print_exc()

def preload_historical_data(C):
    """预加载历史数据"""
    try:
        print("开始预加载历史数据...")

        # 获取所需的最长周期
        max_period = max(60, g.bias_period, g.adx_period*2, g.atr_period*2)
        print(f"计算所需最长周期: {max_period}")

        # 一次性获取所有需要的数据
        for i, security in enumerate(g.stock_list):
            print(f"预加载进度 {i+1}/{len(g.stock_list)}: {security}")
            try:
                # 获取历史数据
                print(f"  正在获取 {security} 的历史数据...")
                hist_data = C.get_market_data_ex(['high', 'low', 'close', 'volume'],
                                               [security],
                                               period='1d',
                                               count=max_period,
                                               subscribe=False)

                print(f"  数据获取结果: {type(hist_data)}")
                if isinstance(hist_data, dict):
                    print(f"  数据字典键: {list(hist_data.keys())}")

                if security in hist_data and len(hist_data[security]) > 0:
                    # 存储到缓存
                    g.cache['price_data'][security] = hist_data[security]
                    print(f"  数据存储到缓存成功，数据长度: {len(hist_data[security])}")

                    # 计算并缓存初始技术指标
                    print(f"  开始计算 {security} 的技术指标...")
                    calculate_initial_indicators(C, security, hist_data[security])
                    print(f"  {security} 技术指标计算完成")

                    print(f"预加载 {security} 历史数据完成，数据长度: {len(hist_data[security])}")
                else:
                    print(f"预加载 {security} 历史数据失败，数据为空")
                    print(f"  hist_data内容: {hist_data}")

            except Exception as e:
                print(f"预加载 {security} 历史数据时出错: {str(e)}")
                import traceback
                traceback.print_exc()
                continue

        print("历史数据预加载完成")

    except Exception as e:
        print(f"预加载历史数据时出错: {str(e)}")
        import traceback
        traceback.print_exc()

def calculate_initial_indicators(C, security, hist_data):
    """计算并缓存初始技术指标"""
    try:
        # QMT平台数据已经是复权后的价格，直接使用
        # 注意：QMT平台的数据默认已经处理了复权，与JQ版本的复权处理效果相同
        closes = hist_data['close'].values
        highs = hist_data['high'].values
        lows = hist_data['low'].values
        volumes = hist_data['volume'].values
        
        # 使用talib计算ADX
        adx = talib.ADX(highs, lows, closes, timeperiod=g.adx_period)
        plus_di = talib.PLUS_DI(highs, lows, closes, timeperiod=g.adx_period)
        minus_di = talib.MINUS_DI(highs, lows, closes, timeperiod=g.adx_period)
        
        # 使用talib计算ATR
        atr = talib.ATR(highs, lows, closes, timeperiod=g.atr_period)
        new_atr_value = atr[-1] if not np.isnan(atr[-1]) else 0.0
        
        # 计算波动率
        returns = pd.Series(closes).pct_change().dropna()
        current_volatility = returns[-20:].std() if len(returns) >= 20 else returns.std()
        historical_volatility = returns.std()
        
        # 计算其他指标
        ma20 = np.mean(closes[-20:]) if len(closes) >= 20 else np.mean(closes)
        ma60 = np.mean(closes[-60:]) if len(closes) >= 60 else np.mean(closes)
        latest_price = closes[-1]
        bias = (latest_price - ma20) / ma20 if ma20 > 0 else 0
        
        # 计算平均成交量
        avg_volume = np.mean(volumes[-20:]) if len(volumes) >= 20 else np.mean(volumes)
        
        # 计算量比
        current_volume = volumes[-1]
        volume_5d_avg = np.mean(volumes[-5:]) if len(volumes) >= 5 else current_volume
        volume_ratio = current_volume / volume_5d_avg if volume_5d_avg > 0 else 1.0
        
        # 计算趋势强度
        trend_strength = np.mean(adx[-20:]) if len(adx) >= 20 else adx[-1] if not np.isnan(adx[-1]) else 25
        
        # 计算M1动量因子
        try:
            if len(closes) >= g.momentum_M1_lookback:
                momentum_M1_value = (closes[-1] / closes[-g.momentum_M1_lookback]) - 1
            else:
                momentum_M1_value = 0.0  # 数据不足时返回0
        except Exception as e:
            print(f"计算{security}的M1动量因子时出错: {str(e)}")
            momentum_M1_value = 0.0  # 出错时返回0
        
        # 存储计算结果
        g.cache['technical_indicators'][security] = {
            'bias': bias,
            'adx': adx[-1] if not np.isnan(adx[-1]) else 25,
            'plus_di': plus_di[-1] if not np.isnan(plus_di[-1]) else 20,
            'minus_di': minus_di[-1] if not np.isnan(minus_di[-1]) else 20,
            'atr': new_atr_value,
            'ma20': ma20,
            'ma60': ma60,
            'volatility': current_volatility,
            'historical_volatility': historical_volatility,
            'avg_volume': avg_volume,
            'trend_strength': trend_strength,
            'latest_price': latest_price,
            'volume_ratio': volume_ratio,
            'momentum_M1': momentum_M1_value,
            'closes': closes,
            'highs': highs,
            'lows': lows,
            'volumes': volumes
        }
        
        print(f"初始化 {security} 技术指标完成")
        
    except Exception as e:
        print(f"计算初始技术指标时出错: {str(e)}")

def update_technical_indicators(C, security):
    """批量更新技术指标，增加缓存机制"""
    print(f"    开始更新 {security} 技术指标")
    current_date = datetime.datetime.now().date()

    # 如果不是新的交易日，且已有缓存，直接返回
    if (g.cache.get('last_update_date') == current_date and
        security in g.cache.get('technical_indicators', {})):
        print(f"    {security} 使用缓存的技术指标")
        return g.cache['technical_indicators'][security]

    try:
        # 获取所需的最长周期
        max_period = max(60, g.bias_period, g.adx_period*2, g.atr_period*2)
        print(f"    计算所需周期: {max_period}")

        # 获取历史数据
        print(f"    正在获取 {security} 历史数据...")
        hist_data = C.get_market_data_ex(['high', 'low', 'close', 'volume'],
                                       [security],
                                       period='1d',
                                       count=max_period,
                                       subscribe=False)

        print(f"    数据获取结果类型: {type(hist_data)}")
        if isinstance(hist_data, dict):
            print(f"    数据字典键: {list(hist_data.keys())}")

        if security not in hist_data or len(hist_data[security]) == 0:
            print(f"    获取 {security} 历史数据失败，数据为空")
            print(f"    hist_data内容: {hist_data}")
            return g.cache.get('technical_indicators', {}).get(security, {})

        data = hist_data[security]
        print(f"    获取到 {security} 数据长度: {len(data)}")
        print(f"    数据列: {list(data.columns) if hasattr(data, 'columns') else 'N/A'}")

        # QMT平台数据已经是复权后的价格，直接使用
        # 注意：QMT平台的数据默认已经处理了复权，与JQ版本的复权处理效果相同
        closes = data['close'].values
        highs = data['high'].values
        lows = data['low'].values
        volumes = data['volume'].values

        print(f"    提取价格数据 - 收盘价长度: {len(closes)}, 最新价格: {closes[-1] if len(closes) > 0 else 'N/A'}")

        # 使用talib计算ADX
        adx = talib.ADX(highs, lows, closes, timeperiod=g.adx_period)
        plus_di = talib.PLUS_DI(highs, lows, closes, timeperiod=g.adx_period)
        minus_di = talib.MINUS_DI(highs, lows, closes, timeperiod=g.adx_period)

        # 使用talib计算ATR
        atr = talib.ATR(highs, lows, closes, timeperiod=g.atr_period)
        new_atr_value = atr[-1] if not np.isnan(atr[-1]) else 0.0

        # 计算波动率
        returns = pd.Series(closes).pct_change().dropna()
        current_volatility = returns[-20:].std() if len(returns) >= 20 else returns.std()
        historical_volatility = returns.std()

        # 计算其他指标
        ma20 = np.mean(closes[-20:]) if len(closes) >= 20 else np.mean(closes)
        ma60 = np.mean(closes[-60:]) if len(closes) >= 60 else np.mean(closes)
        latest_price = closes[-1]
        bias = (latest_price - ma20) / ma20 if ma20 > 0 else 0

        # 计算平均成交量
        avg_volume = np.mean(volumes[-20:]) if len(volumes) >= 20 else np.mean(volumes)

        # 计算量比
        current_volume = volumes[-1]
        volume_5d_avg = np.mean(volumes[-5:]) if len(volumes) >= 5 else current_volume
        volume_ratio = current_volume / volume_5d_avg if volume_5d_avg > 0 else 1.0

        # 计算趋势强度
        trend_strength = np.mean(adx[-20:]) if len(adx) >= 20 else adx[-1] if not np.isnan(adx[-1]) else 25

        # 计算M1动量因子
        try:
            if len(closes) >= g.momentum_M1_lookback:
                momentum_M1_value = (closes[-1] / closes[-g.momentum_M1_lookback]) - 1
            else:
                momentum_M1_value = 0.0
        except Exception as e:
            print(f"计算{security}的M1动量因子时出错: {str(e)}")
            momentum_M1_value = 0.0

        # 存储计算结果
        if 'technical_indicators' not in g.cache:
            g.cache['technical_indicators'] = {}

        indicators_to_update = {
            'bias': bias,
            'adx': adx[-1] if not np.isnan(adx[-1]) else 25,
            'plus_di': plus_di[-1] if not np.isnan(plus_di[-1]) else 20,
            'minus_di': minus_di[-1] if not np.isnan(minus_di[-1]) else 20,
            'atr': new_atr_value,
            'ma20': ma20,
            'ma60': ma60,
            'volatility': current_volatility,
            'historical_volatility': historical_volatility,
            'avg_volume': avg_volume,
            'trend_strength': trend_strength,
            'latest_price': latest_price,
            'volume_ratio': volume_ratio,
            'momentum_M1': momentum_M1_value,
            'closes': closes,
            'highs': highs,
            'lows': lows,
            'volumes': volumes
        }

        if security not in g.cache['technical_indicators']:
            g.cache['technical_indicators'][security] = {}
        g.cache['technical_indicators'][security].update(indicators_to_update)
        g.cache['last_update_date'] = current_date

        return g.cache['technical_indicators'][security]

    except Exception as e:
        print(f"更新 {security} 技术指标时出错: {str(e)}")
        default_indicators = {
            'bias': 0, 'adx': 25, 'plus_di': 20, 'minus_di': 20, 'atr': 0.0,
            'ma20': 0, 'ma60': 0, 'volatility': 0.02, 'historical_volatility': 0.02,
            'avg_volume': 1e6, 'trend_strength': 20, 'latest_price': 0,
            'volume_ratio': 1.0, 'momentum_M1': 0.0
        }
        return g.cache.get('technical_indicators', {}).get(security, default_indicators)

def market_open(C):
    """开盘时运行"""
    try:
        print("开盘时任务执行")

        # 检查是否需要建立初始仓位
        if g.initial_buy_needed and not g.initial_position_established:
            print("准备进行初始买入")
            execute_initial_buy(C)
            g.initial_buy_needed = False

        # 取消所有未完成订单
        cancel_all_orders(C)

        # 检查是否需要月度再平衡
        if should_rebalance():
            print("执行月度再平衡")
            rebalance_portfolio(C)

        # 更新市场状态
        for security in g.stock_list:
            old_state = g.market_states[security]
            new_state = get_market_state(C, security)
            if old_state != new_state:
                # 只在状态发生显著变化时记录
                if (old_state == 'oscillating' or new_state == 'oscillating') and abs(get_trend_strength(security)) > 20:
                    print(f"{security} 市场状态从 {old_state} 变为 {new_state}")
                g.market_states[security] = new_state

    except Exception as e:
        print(f"market_open出错: {str(e)}")

def should_rebalance():
    """判断是否需要再平衡"""
    try:
        current_date = datetime.datetime.now().date()

        # 使用缓存检查再平衡
        if g.cache['last_rebalance_check'] == current_date:
            return False

        # 修改为每月最后一个交易日进行再平衡（与JQ版本保持一致）
        current_month = current_date.month
        next_month = current_month + 1 if current_month < 12 else 1
        next_month_first_day = datetime.date(current_date.year + (current_month == 12), next_month, 1)
        current_month_first_day = datetime.date(current_date.year, current_date.month, 1)

        # 简化的交易日判断（QMT平台限制）
        # 假设每月最后5个自然日中的工作日为月末交易日
        if current_date.day >= 25 and current_date.weekday() < 5:  # 工作日
            if g.last_rebalance_date is not None:
                if g.last_rebalance_date.year == current_date.year and g.last_rebalance_date.month == current_date.month:
                    return False
            g.cache['last_rebalance_check'] = current_date
            return True
        return False
    except Exception as e:
        print(f"判断再平衡时出错: {str(e)}")
        return False

def get_market_state(C, security):
    """
    根据技术指标判断市场状态
    返回: 'uptrend', 'downtrend', 或 'oscillating'
    """
    try:
        # 更新技术指标
        indicators = update_technical_indicators(C, security)

        # 获取BIAS和ADX值
        bias = indicators.get('bias', 0)
        adx = indicators.get('adx', 0)
        plus_di = indicators.get('plus_di', 0)
        minus_di = indicators.get('minus_di', 0)

        # 获取该证券的BIAS阈值
        bias_threshold = g.bias_threshold[security]

        # 趋势判断标准
        TREND_THRESHOLD = 35  # ADX高于35即为趋势
        # 震荡市判定标准放宽
        if adx < TREND_THRESHOLD or abs(bias) < bias_threshold * g.bias_oscillation_scale_factor:
            return 'oscillating'

        # 判断市场状态
        if adx > TREND_THRESHOLD:  # 存在明显趋势
            if plus_di > minus_di:  # 上升趋势
                if bias > bias_threshold:  # BIAS过高，可能超买
                    return 'oscillating'
                return 'uptrend'
            else:  # 下降趋势
                if bias < -bias_threshold:  # BIAS过低，可能超卖
                    return 'oscillating'
                return 'downtrend'
        else:  # ADX较低，无明显趋势
            if abs(bias) > bias_threshold:  # BIAS超出阈值
                if bias > bias_threshold:  # 可能超买
                    return 'downtrend'
                else:  # 可能超卖
                    return 'uptrend'
            return 'oscillating'  # 震荡市场

    except Exception as e:
        print(f"判断市场状态时出错: {str(e)}")
        return 'oscillating'  # 发生错误时默认返回震荡状态

def get_trend_strength(security):
    """计算趋势强度"""
    try:
        indicators = g.cache['technical_indicators'].get(security)
        if indicators and 'trend_strength' in indicators:
            return indicators['trend_strength']
        return 20  # 默认中等趋势
    except Exception as e:
        print(f"计算趋势强度时出错: {str(e)}")
        return 20

def cancel_all_orders(C):
    """取消所有未完成的订单"""
    try:
        # QMT中获取未完成委托
        orders = get_trade_detail_data(g.account_id, 'stock', 'order')
        if orders:
            cancel_count = 0
            for order in orders:
                # 检查委托状态，只撤销未完成的委托
                if order.m_nOrderStatus in [48, 49, 50]:  # 未完成状态
                    # 撤销委托
                    passorder(order.m_nOrderType + 1000, 1101, g.account_id,
                             order.m_strInstrumentID + '.' + order.m_strExchangeID,
                             5, -1, 0, f"撤销委托{order.m_strOrderSysID}", 2, "", C)
                    cancel_count += 1
            if cancel_count > 0:
                print(f"取消了 {cancel_count} 个未完成订单")
    except Exception as e:
        print(f"取消订单时出错: {str(e)}")

def check_stop_loss(C):
    """检查止损条件"""
    try:
        # 获取当前持仓
        positions = get_trade_detail_data(g.account_id, 'stock', 'position')
        position_dict = {pos.m_strInstrumentID + '.' + pos.m_strExchangeID: pos
                        for pos in positions if pos.m_nVolume > 0}

        # 获取账户信息
        accounts = get_trade_detail_data(g.account_id, 'stock', 'account')
        if not accounts:
            print("无法获取账户信息")
            return
        account = accounts[0]

        for security in g.stock_list:
            if security in position_dict:
                position = position_dict[security]

                # 获取当前价格
                tick_data = C.get_full_tick([security])
                if security not in tick_data:
                    continue

                current_price = tick_data[security]['lastPrice']
                cost_price = position.m_dCostPrice

                # 计算收益率
                returns = (current_price - cost_price) / cost_price if cost_price > 0 else 0
                stop_loss_line = calculate_dynamic_stop_loss(security)

                if returns <= -stop_loss_line:
                    print(f"触发止损 - {security}: 收益率={returns:.2%}, 止损线={stop_loss_line:.2%}")
                    sell_amount = int(position.m_nVolume * 0.5)
                    if sell_amount >= 100:
                        passorder(24, 1101, g.account_id, security, 5, -1, sell_amount,
                                 f"分批止损{security}", 2, f"止损卖出{sell_amount}股", C)
                        print(f"分批止损 - {security}: 卖出{sell_amount}股")
                    else:
                        passorder(24, 1101, g.account_id, security, 5, -1, position.m_nVolume,
                                 f"完全止损{security}", 2, f"止损清仓{position.m_nVolume}股", C)
                        print(f"完全止损 - {security}: 清仓")
                    g.stop_trading = True
                    return

        # 检查组合止损
        portfolio_returns = (account.m_dBalance - account.m_dPreBalance) / account.m_dPreBalance if account.m_dPreBalance > 0 else 0
        if portfolio_returns <= -g.portfolio_stop_loss:
            print(f"触发组合止损: 收益率={portfolio_returns:.2%}")
            g.stop_trading = True

    except Exception as e:
        print(f"检查止损时出错: {str(e)}")

def calculate_dynamic_stop_loss(security):
    """计算动态止损线"""
    try:
        # 获取市场状态和波动率
        market_state = g.market_states[security]
        indicators = g.cache['technical_indicators'].get(security, {})
        volatility = indicators.get('volatility', 0.02)

        # 基础止损线
        base_stop_loss = g.single_stock_stop_loss

        # 根据市场状态调整
        if market_state == 'uptrend':
            stop_loss_factor = 1.2  # 上涨趋势放宽止损
        elif market_state == 'downtrend':
            stop_loss_factor = 0.8  # 下跌趋势收紧止损
        else:
            stop_loss_factor = 1.0  # 震荡市保持基础止损

        # 根据波动率调整
        volatility_factor = 1.0 + (volatility - 0.02) * 2  # 波动率每增加1%，止损放宽2%
        volatility_factor = max(0.8, min(1.5, volatility_factor))  # 限制调整范围

        # 计算最终止损线
        final_stop_loss = base_stop_loss * stop_loss_factor * volatility_factor

        return max(0.03, min(0.08, final_stop_loss))  # 限制止损线在3%-8%之间

    except Exception as e:
        print(f"计算动态止损线时出错: {str(e)}")
        return g.single_stock_stop_loss

def trade_money_fund(C):
    """交易货币基金"""
    try:
        # 获取账户信息
        accounts = get_trade_detail_data(g.account_id, 'stock', 'account')
        if not accounts:
            return
        account = accounts[0]

        # 计算大额闲置资金阈值
        total_value = account.m_dBalance
        min_cash_buffer = total_value * 0.01  # 1%现金缓冲
        available_cash = account.m_dAvailable - min_cash_buffer

        # 只有当闲置资金大于1万元时才买入货基
        if available_cash > 10000:
            # 获取货基当前价格
            tick_data = C.get_full_tick([g.money_fund])
            if g.money_fund in tick_data:
                current_price = tick_data[g.money_fund]['lastPrice']

                # 获取当前货基持仓
                positions = get_trade_detail_data(g.account_id, 'stock', 'position')
                current_amount = 0
                for pos in positions:
                    if pos.m_strInstrumentID + '.' + pos.m_strExchangeID == g.money_fund:
                        current_amount = pos.m_nVolume
                        break

                target_amount = int(available_cash / (current_price * 100)) * 100
                if abs(target_amount - current_amount) > 1000:
                    # 调整货基持仓
                    if target_amount > current_amount:
                        buy_amount = target_amount - current_amount
                        passorder(23, 1101, g.account_id, g.money_fund, 5, -1, buy_amount,
                                 "货基买入", 2, f"货基调整买入{buy_amount}份", C)
                    else:
                        sell_amount = current_amount - target_amount
                        passorder(24, 1101, g.account_id, g.money_fund, 5, -1, sell_amount,
                                 "货基卖出", 2, f"货基调整卖出{sell_amount}份", C)
                    print(f"货基调整: {target_amount-current_amount:+d}份")

    except Exception as e:
        print(f"货基交易过程中发生错误: {str(e)}")

def check_fund_income(C):
    """检查货基收益情况"""
    try:
        # 获取货基持仓
        positions = get_trade_detail_data(g.account_id, 'stock', 'position')
        for pos in positions:
            if pos.m_strInstrumentID + '.' + pos.m_strExchangeID == g.money_fund and pos.m_nVolume > 0:
                # 获取当前价格
                tick_data = C.get_full_tick([g.money_fund])
                if g.money_fund in tick_data:
                    current_price = tick_data[g.money_fund]['lastPrice']
                    cost = pos.m_dCostPrice * pos.m_nVolume
                    current_value = current_price * pos.m_nVolume
                    daily_return = (current_value - cost) / cost if cost > 0 else 0

                    # 只在收益率超过0.01%时记录日志
                    if abs(daily_return) > 0.0001:
                        print(f"货基当日收益: {daily_return:.4%}")
                break

    except Exception as e:
        print(f"检查货基收益时发生错误: {str(e)}")

def monitor_strategy_performance(C):
    """监控策略性能"""
    try:
        # 获取账户信息
        accounts = get_trade_detail_data(g.account_id, 'stock', 'account')
        if not accounts:
            return
        account = accounts[0]

        # 计算当日收益
        daily_return = (account.m_dBalance - account.m_dPreBalance) / account.m_dPreBalance if account.m_dPreBalance > 0 else 0

        # 计算波动率
        portfolio_volatility = calculate_portfolio_volatility()

        # 计算夏普比率
        sharpe_ratio = daily_return / portfolio_volatility if portfolio_volatility > 0 else 0

        # 检查风险指标
        if daily_return < -0.02:  # 单日亏损超过2%
            print(f"策略单日亏损较大: {daily_return:.2%}")

        if portfolio_volatility > 0.03:  # 波动率超过3%
            print(f"策略波动率较高: {portfolio_volatility:.2%}")

        if sharpe_ratio < 0.5:  # 夏普比率低于0.5
            print(f"策略夏普比率较低: {sharpe_ratio:.2f}")

        # 记录性能指标
        print(f"策略性能指标 - 日收益: {daily_return:.2%}, 波动率: {portfolio_volatility:.2%}, 夏普比率: {sharpe_ratio:.2f}")

    except Exception as e:
        print(f"监控策略性能时出错: {str(e)}")

def calculate_portfolio_volatility():
    """计算投资组合的波动率"""
    try:
        # 获取每个标的的波动率
        volatilities = []
        for security in g.stock_list:
            indicators = g.cache['technical_indicators'].get(security)
            if indicators and 'volatility' in indicators:
                volatilities.append(indicators['volatility'])

        # 计算平均波动率
        if volatilities:
            return sum(volatilities) / len(volatilities)
        return 0.02  # 默认波动率
    except Exception as e:
        print(f"计算投资组合波动率时出错: {str(e)}")
        return 0.02  # 默认波动率

def calculate_risk_parity_weights(C):
    """计算风险平价权重"""
    try:
        # 初始化波动率字典
        volatilities = {}

        # 计算每个ETF的60日波动率
        for security in g.stock_list:
            try:
                # 获取过去60个交易日的收盘价
                hist_data = C.get_market_data_ex(['close'], [security],
                                               period='1d', count=60, subscribe=False)

                if security in hist_data and len(hist_data[security]) > 0:
                    closes = hist_data[security]['close'].values

                    # 计算日收益率
                    returns = pd.Series(closes).pct_change().dropna()

                    # 计算波动率（标准差）
                    volatility = returns.std()
                    volatilities[security] = volatility
                else:
                    volatilities[security] = 0.02  # 使用默认波动率

            except Exception as e:
                print(f"计算 {security} 波动率时出错: {str(e)}")
                volatilities[security] = 0.02  # 使用默认波动率

        # 计算权重：1/波动率 / 所有(1/波动率)之和
        inv_vol_sum = sum(1.0 / vol for vol in volatilities.values() if vol > 0)
        if inv_vol_sum == 0:
            # 如果所有波动率都为0，使用等权重
            equal_weight = 1.0 / len(g.stock_list)
            return {security: equal_weight for security in g.stock_list}

        weights = {security: (1.0 / vol) / inv_vol_sum for security, vol in volatilities.items() if vol > 0}

        # 优化：将权重转换为整数百分比
        total_pct = 100
        int_weights = {}
        remaining = total_pct

        # 先将权重转换为整数百分比（向下取整）
        for security, weight in weights.items():
            int_weight = int(weight * 100)
            int_weights[security] = int_weight
            remaining -= int_weight

        # 将剩余的百分比按原权重比例分配
        if remaining > 0:
            # 按原始权重排序，权重大的优先获得剩余百分比
            sorted_securities = sorted(weights.keys(), key=lambda x: weights[x], reverse=True)
            for i in range(remaining):
                security = sorted_securities[i % len(sorted_securities)]
                int_weights[security] += 1

        # 转换回小数形式
        rounded_weights = {security: weight / 100.0 for security, weight in int_weights.items()}

        # 输出权重信息
        weight_str = ', '.join([f"{security}: {weight:.0%}" for security, weight in rounded_weights.items()])
        print(f"风险平价权重计算结果: {weight_str}")

        return rounded_weights

    except Exception as e:
        print(f"计算风险平价权重时出错: {str(e)}")
        # 出错时返回等权重
        equal_weight = 1.0 / len(g.stock_list)
        return {security: equal_weight for security in g.stock_list}

def execute_initial_buy(C):
    """执行初始建仓"""
    try:
        # 计算风险平价权重
        weights = calculate_risk_parity_weights(C)

        # 获取账户信息
        accounts = get_trade_detail_data(g.account_id, 'stock', 'account')
        if not accounts:
            print("无法获取账户信息")
            return
        account = accounts[0]

        # 记录原始备用资金比例
        original_reserve_pct = g.reserve_position_pct
        current_reserve_pct = original_reserve_pct

        # 分析整体市场状况
        up_count = 0
        down_count = 0
        for security in g.stock_list:
            market_state = get_market_state(C, security)
            if market_state == 'uptrend':
                up_count += 1
            elif market_state == 'downtrend':
                down_count += 1

        # 根据整体市场状况调整备用资金
        if up_count == len(g.stock_list):
            # 全面上涨行情，降低备用资金
            current_reserve_pct = max(0.1, original_reserve_pct * g.reserve_scale_down_factor)
            print(f'初始建仓：全面上涨行情，备用资金调整为{current_reserve_pct:.1%}')
        elif down_count == len(g.stock_list):
            # 全面下跌行情，提高备用资金
            current_reserve_pct = min(0.5, original_reserve_pct * g.reserve_scale_up_factor)
            print(f'初始建仓：全面下跌行情，备用资金调整为{current_reserve_pct:.1%}')

        # 执行建仓
        total_value = account.m_dBalance
        available_cash = account.m_dAvailable

        for security in g.stock_list:
            try:
                # 计算目标金额
                target_value = total_value * (1 - current_reserve_pct) * weights.get(security, 0)

                # 获取当前价格
                tick_data = C.get_full_tick([security])
                if security not in tick_data:
                    print(f"{security} 无法获取行情数据，跳过建仓")
                    continue

                current_price = tick_data[security]['lastPrice']

                # 计算购买数量（确保是100的整数倍）
                amount = int(target_value / current_price / 100) * 100

                if amount >= 100:  # 确保购买数量至少为100股
                    order_value = amount * current_price
                    if order_value <= available_cash:  # 确保资金充足
                        passorder(23, 1101, g.account_id, security, 5, -1, amount,
                                 f"初始建仓{security}", 2, f"初始建仓{amount}股", C)
                        print(f"初始建仓 {security}: 数量={amount}, 价格={current_price:.3f}, 金额={order_value:.2f}, 权重={weights[security]:.1%}")
                        available_cash -= order_value
                    else:
                        print(f"初始建仓 {security}: 资金不足，需要{order_value:.2f}，可用{available_cash:.2f}")
                else:
                    print(f"初始建仓 {security}: 计算购买数量小于100股，跳过购买")
            except Exception as e:
                print(f"处理标的 {security} 时发生错误: {str(e)}")
                continue

        print("初始建仓完成")
        g.initial_position_established = True

    except Exception as e:
        print(f"执行初始建仓时发生错误: {str(e)}")

def rebalance_portfolio(C):
    """再平衡投资组合"""
    try:
        # 计算风险平价权重
        weights = calculate_risk_parity_weights(C)

        # 获取账户信息
        accounts = get_trade_detail_data(g.account_id, 'stock', 'account')
        if not accounts:
            print("无法获取账户信息")
            return
        account = accounts[0]

        # 记录原始备用资金比例
        original_reserve_pct = g.reserve_position_pct
        current_reserve_pct = original_reserve_pct

        # 优化：动态调整备用资金比例
        up_count = sum(1 for s in g.stock_list if g.market_states[s] == 'uptrend')
        down_count = sum(1 for s in g.stock_list if g.market_states[s] == 'downtrend')

        # 根据整体市场状况调整备用资金
        if up_count == len(g.stock_list):
            current_reserve_pct = max(0.1, original_reserve_pct * g.reserve_scale_down_factor)
            print(f'全面上涨行情，备用资金调整为{current_reserve_pct:.1%}')
        elif down_count == len(g.stock_list):
            current_reserve_pct = min(0.5, original_reserve_pct * g.reserve_scale_up_factor)
            print(f'全面下跌行情，备用资金调整为{current_reserve_pct:.1%}')

        # 处理货基持仓
        positions = get_trade_detail_data(g.account_id, 'stock', 'position')
        for pos in positions:
            if pos.m_strInstrumentID + '.' + pos.m_strExchangeID == g.money_fund and pos.m_nVolume > 0:
                passorder(24, 1101, g.account_id, g.money_fund, 5, -1, pos.m_nVolume,
                         "再平衡清空货基", 2, f"再平衡清空货基{pos.m_nVolume}份", C)
                print(f"再平衡: 清空货基持仓 {pos.m_nVolume}份")
                break

        # 调整持仓
        total_position_value = account.m_dBalance * (1 - current_reserve_pct)
        for security in g.stock_list:
            target_value = total_position_value * weights[security]

            # 获取当前持仓
            current_position = 0
            current_value = 0
            for pos in positions:
                if pos.m_strInstrumentID + '.' + pos.m_strExchangeID == security:
                    current_position = pos.m_nVolume
                    current_value = pos.m_nVolume * pos.m_dCostPrice
                    break

            # 获取当前价格
            tick_data = C.get_full_tick([security])
            if security not in tick_data:
                continue
            current_price = tick_data[security]['lastPrice']

            # 计算目标持仓数量
            target_amount = int(target_value / current_price / 100) * 100

            # 调整持仓
            if target_amount != current_position:
                if target_amount > current_position:
                    # 买入
                    buy_amount = target_amount - current_position
                    passorder(23, 1101, g.account_id, security, 5, -1, buy_amount,
                             f"再平衡买入{security}", 2, f"再平衡买入{buy_amount}股", C)
                    print(f"再平衡买入 {security}: {buy_amount}股")
                else:
                    # 卖出
                    sell_amount = current_position - target_amount
                    passorder(24, 1101, g.account_id, security, 5, -1, sell_amount,
                             f"再平衡卖出{security}", 2, f"再平衡卖出{sell_amount}股", C)
                    print(f"再平衡卖出 {security}: {sell_amount}股")

                print(f"调整 {security} 目标市值至: {target_value:.2f}, 权重: {weights[security]:.2%}")

        # 更新最后再平衡时间
        g.last_rebalance_date = datetime.datetime.now().date()

    except Exception as e:
        print(f"执行再平衡时发生错误: {str(e)}")

def apply_momentum_overlay(C):
    """应用动量叠加策略"""
    try:
        # 检查是否已经执行过本周的动量调整
        current_date = datetime.datetime.now().date()
        if g.cache['last_momentum_check'] == current_date:
            return

        # 更新技术指标
        for security in g.stock_list:
            update_technical_indicators(C, security)

        # 获取基础风险平价权重
        base_weights = calculate_risk_parity_weights(C)

        # 获取账户信息
        accounts = get_trade_detail_data(g.account_id, 'stock', 'account')
        if not accounts:
            return
        account = accounts[0]

        # 计算实际可用的备用金
        total_value = account.m_dBalance
        available_cash = account.m_dAvailable

        # 计算货基市值
        money_fund_value = 0
        positions = get_trade_detail_data(g.account_id, 'stock', 'position')
        for pos in positions:
            if pos.m_strInstrumentID + '.' + pos.m_strExchangeID == g.money_fund and pos.m_nVolume > 0:
                tick_data = C.get_full_tick([g.money_fund])
                if g.money_fund in tick_data:
                    money_fund_value = pos.m_nVolume * tick_data[g.money_fund]['lastPrice']
                break

        # 计算实际可用的备用金总额
        actual_reserve_value = available_cash + money_fund_value
        actual_reserve_pct = actual_reserve_value / total_value if total_value > 0 else 0

        # 设置最大可用备用金（使用实际值的一半）
        max_total_boost = min(actual_reserve_pct / 2, g.max_momentum_allocation_pct)

        # 识别动量资产 - 使用M1动量因子
        momentum_assets = []
        for security in g.stock_list:
            try:
                indicators = g.cache['technical_indicators'].get(security)
                if not indicators:
                    print(f"{security} 没有技术指标数据，跳过动量判断")
                    continue

                # 检查必要的指标是否存在
                if 'latest_price' not in indicators or 'momentum_M1' not in indicators:
                    print(f"{security} 缺少必要的技术指标数据，跳过动量判断")
                    continue

                # 使用M1动量因子判断动量信号
                momentum_strength = indicators.get('momentum_M1', 0.0)
                if momentum_strength > g.momentum_threshold:
                    momentum_assets.append((security, momentum_strength))
                    print(f"{security} 满足M1动量条件: 价格={indicators['latest_price']:.2f}, M1动量={momentum_strength:.2%}")
            except Exception as e:
                print(f"处理{security}的动量判断时出错: {str(e)}")
                continue

        # 按动量强度排序
        momentum_assets.sort(key=lambda x: x[1], reverse=True)

        # 初始化最终权重
        final_weights = base_weights.copy()
        total_boost_weight = 0.0
        boost_allocations = {}  # 记录每个资产的增强分配

        # 计算动量调整
        for security, momentum_strength in momentum_assets:
            # 计算该资产的权重增量
            base_weight = float(base_weights[security])
            boost = base_weight * (g.momentum_boost_factor - 1)

            # 根据动量强度调整增强系数
            strength_factor = min(1.5, 1 + momentum_strength * 2)
            adjusted_boost = boost * strength_factor

            # 检查是否超过最大增强限制
            if total_boost_weight + adjusted_boost <= max_total_boost:
                final_weights[security] = base_weight + adjusted_boost
                total_boost_weight += adjusted_boost
                boost_allocations[security] = adjusted_boost
                print(f"动量增强 {security}: 基础权重 {base_weight:.1%} -> 最终权重 {final_weights[security]:.1%}")
            else:
                print(f"动量增强 {security}: 超过最大增强限制，跳过")

        # 如果有动量调整，执行交易
        if boost_allocations:
            print(f"执行动量叠加调整，总增强权重: {total_boost_weight:.1%}")

            # 执行动量调整交易
            for security in g.stock_list:
                if security in boost_allocations:
                    boost_value = total_value * boost_allocations[security]

                    # 获取当前价格
                    tick_data = C.get_full_tick([security])
                    if security not in tick_data:
                        continue
                    current_price = tick_data[security]['lastPrice']

                    # 计算增强买入数量
                    boost_amount = int(boost_value / current_price / 100) * 100

                    if boost_amount >= 100:
                        passorder(23, 1101, g.account_id, security, 5, -1, boost_amount,
                                 f"动量增强{security}", 2, f"动量增强买入{boost_amount}股", C)
                        print(f"动量增强买入 {security}: {boost_amount}股，金额: {boost_value:.2f}")
        else:
            print("本周无满足条件的动量资产")

        # 更新缓存
        g.cache['last_momentum_check'] = current_date

    except Exception as e:
        print(f"应用动量叠加策略时出错: {str(e)}")

# 辅助函数
def calculate_dynamic_allocation_factors(security):
    """计算动态配置调整因子"""
    try:
        indicators = g.cache['technical_indicators'].get(security)
        if not indicators:
            return 1.0, 0.5

        # 获取关键指标
        trend_strength = indicators.get('trend_strength', 20)
        volatility = indicators.get('volatility', 0.02)
        volume_ratio = indicators.get('volume_ratio', 1.0)
        bias = indicators.get('bias', 0)
        adx = indicators.get('adx', 25)
        plus_di = indicators.get('plus_di', 20)
        minus_di = indicators.get('minus_di', 20)

        # 计算各种因子
        trend_factor = min(2.0, max(0.5, trend_strength / 20))

        direction_factor = 1.0
        if plus_di > minus_di:
            direction_factor = min(1.5, max(1.0, 1 + (plus_di - minus_di) / 100))
        else:
            direction_factor = min(1.0, max(0.8, 1 - (minus_di - plus_di) / 100))

        vol_base = 0.02
        if volatility < vol_base:
            volatility_factor = min(1.5, vol_base / volatility)
        else:
            volatility_factor = max(0.6, vol_base / volatility)

        if volume_ratio > 1:
            volume_factor = min(1.8, 1 + (volume_ratio - 1) * 0.5)
        else:
            volume_factor = max(0.7, 1 - (1 - volume_ratio) * 0.5)

        bias_threshold = g.bias_threshold[security]
        if abs(bias) < bias_threshold * 0.5:
            bias_factor = 1.6 - abs(bias) / bias_threshold
        else:
            bias_factor = max(0.6, 1 - abs(bias) / bias_threshold)

        trend_persistence = min(1.5, adx / g.alloc_persistence_adx_base)

        # 综合计算调整系数
        adjustment_factor = (
            trend_factor * g.weight_trend +
            direction_factor * g.weight_direction +
            volatility_factor * g.weight_volatility +
            volume_factor * g.weight_volume +
            bias_factor * g.weight_bias
        ) * trend_persistence

        # 计算置信度
        confidence = min(1.0, (
            (trend_strength / g.conf_trend_base) * g.conf_trend_weight +
            (1 - abs(bias) / (bias_threshold * g.conf_bias_mult)) * g.conf_bias_weight +
            (adx / g.conf_adx_base) * g.conf_adx_weight +
            (volume_ratio - g.conf_vol_offset) * g.conf_vol_weight
        ))

        # 确保最终调整系数在合理范围内
        adjustment_factor = min(2.5, max(0.5, adjustment_factor))

        return adjustment_factor, confidence

    except Exception as e:
        print(f"计算动态配置因子时出错: {str(e)}")
        return 1.0, 0.5
