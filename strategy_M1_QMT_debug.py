#coding:gbk

# 简化的调试版本策略 - 用于定位回测暂停问题
import pandas as pd
import numpy as np
import datetime
import math

# 全局变量类，用于保存策略状态
class StrategyContext:
    def __init__(self):
        # 设置交易标的
        self.stock_list = ['513100.SH',  # 纳斯达克ETF
                          '510300.SH',  # 沪深300ETF
                          '518880.SH']  # 黄金ETF
        
        # QMT特有参数
        self.account_id = "strategy_M1"
        self.debug_step = 0
        self.last_bar_time = None

# 创建全局策略上下文
g = StrategyContext()

def init(C):
    """初始化函数 - 调试版本"""
    print("=" * 80)
    print("调试版本策略初始化开始...")
    print("=" * 80)
    
    try:
        g.debug_step = 1
        print(f"调试步骤 {g.debug_step}: 设置账户信息")
        g.account_id = C.accountid if hasattr(C, 'accountid') else "strategy_M1"
        print(f"账户ID设置完成: {g.account_id}")
        
        g.debug_step = 2
        print(f"调试步骤 {g.debug_step}: 检查C对象属性")
        print(f"C对象类型: {type(C)}")
        print(f"C对象属性: {dir(C)}")
        
        g.debug_step = 3
        print(f"调试步骤 {g.debug_step}: 测试基本数据获取")
        try:
            # 测试获取单个标的的少量数据
            test_security = g.stock_list[0]
            print(f"测试获取 {test_security} 的数据...")
            
            # 尝试获取最近5天的数据
            hist_data = C.get_market_data_ex(['close'], [test_security], 
                                           period='1d', count=5, subscribe=False)
            print(f"数据获取成功，类型: {type(hist_data)}")
            if isinstance(hist_data, dict):
                print(f"数据字典键: {list(hist_data.keys())}")
                if test_security in hist_data:
                    print(f"{test_security} 数据长度: {len(hist_data[test_security])}")
                    print(f"{test_security} 数据类型: {type(hist_data[test_security])}")
                    if hasattr(hist_data[test_security], 'columns'):
                        print(f"{test_security} 数据列: {list(hist_data[test_security].columns)}")
                else:
                    print(f"警告: {test_security} 不在返回的数据中")
            else:
                print(f"警告: 返回的数据不是字典类型")
                
        except Exception as e:
            print(f"数据获取测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
        
        g.debug_step = 4
        print(f"调试步骤 {g.debug_step}: 测试行情数据获取")
        try:
            tick_data = C.get_full_tick(g.stock_list)
            print(f"行情数据获取成功，类型: {type(tick_data)}")
            if isinstance(tick_data, dict):
                print(f"行情数据键: {list(tick_data.keys())}")
                for security in g.stock_list:
                    if security in tick_data:
                        print(f"{security} 行情数据可用")
                    else:
                        print(f"警告: {security} 行情数据不可用")
            else:
                print(f"警告: 行情数据不是字典类型")
        except Exception as e:
            print(f"行情数据获取测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
        
        g.debug_step = 5
        print(f"调试步骤 {g.debug_step}: 测试账户数据获取")
        try:
            # 尝试获取账户信息
            accounts = get_trade_detail_data(g.account_id, 'stock', 'account')
            print(f"账户数据获取成功，类型: {type(accounts)}")
            if accounts:
                print(f"账户数量: {len(accounts)}")
                account = accounts[0]
                print(f"账户对象类型: {type(account)}")
                print(f"账户对象属性: {dir(account)}")
            else:
                print("警告: 未获取到账户数据")
        except Exception as e:
            print(f"账户数据获取测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
        
        g.debug_step = 6
        print(f"调试步骤 {g.debug_step}: 初始化完成")
        print("=" * 80)
        print("调试版本策略初始化成功完成!")
        print("=" * 80)
        
    except Exception as e:
        print("=" * 80)
        print(f"调试版本策略初始化失败，步骤 {g.debug_step}: {str(e)}")
        print("=" * 80)
        import traceback
        traceback.print_exc()
        raise e

def handlebar(C):
    """主要的策略执行函数 - 调试版本"""
    try:
        print(f"\n{'='*80}")
        print(f"调试版本handlebar开始执行")
        print(f"{'='*80}")
        
        # 检查是否为最新K线
        print("步骤1: 检查K线状态")
        try:
            is_last = C.is_last_bar()
            print(f"是否为最新K线: {is_last}")
        except Exception as e:
            print(f"检查K线状态失败: {str(e)}")
            is_last = True  # 假设是最新K线继续执行
        
        # 跳过历史K线，只处理最新K线
        if not is_last:
            print("跳过历史K线，退出handlebar")
            return
            
        # 获取当前时间
        print("步骤2: 获取当前时间")
        current_time = datetime.datetime.now()
        current_date = current_time.date()
        current_time_str = current_time.strftime('%H%M%S')
        print(f"当前时间: {current_time}")
        print(f"当前日期: {current_date}")
        print(f"时间字符串: {current_time_str}")
        
        # 防止重复执行
        print("步骤3: 检查重复执行")
        if g.last_bar_time == current_time:
            print("防止重复执行，跳过")
            return
        g.last_bar_time = current_time
        print("通过重复执行检查")
        
        # 测试数据获取
        print("步骤4: 测试数据获取")
        for i, security in enumerate(g.stock_list):
            print(f"测试数据获取 {i+1}/{len(g.stock_list)}: {security}")
            try:
                # 获取最近3天的数据
                hist_data = C.get_market_data_ex(['close'], [security], 
                                               period='1d', count=3, subscribe=False)
                if security in hist_data and len(hist_data[security]) > 0:
                    latest_price = hist_data[security]['close'].iloc[-1]
                    print(f"  {security} 最新价格: {latest_price}")
                else:
                    print(f"  {security} 数据获取失败")
            except Exception as e:
                print(f"  {security} 数据获取异常: {str(e)}")
        
        # 测试行情数据
        print("步骤5: 测试行情数据")
        try:
            tick_data = C.get_full_tick(g.stock_list)
            for security in g.stock_list:
                if security in tick_data:
                    last_price = tick_data[security].get('lastPrice', 'N/A')
                    print(f"  {security} 实时价格: {last_price}")
                else:
                    print(f"  {security} 实时数据不可用")
        except Exception as e:
            print(f"行情数据获取异常: {str(e)}")
        
        print(f"{'='*80}")
        print(f"调试版本handlebar执行完成")
        print(f"{'='*80}\n")
            
    except Exception as e:
        print(f"{'='*80}")
        print(f"调试版本handlebar执行出错: {str(e)}")
        print(f"{'='*80}")
        import traceback
        traceback.print_exc()

# 简化的下载函数
def download_historical_data(C):
    """下载历史数据 - 调试版本"""
    try:
        print("调试版本：开始下载历史数据...")
        
        # 只下载少量数据进行测试
        for i, security in enumerate(g.stock_list):
            print(f"下载进度 {i+1}/{len(g.stock_list)}: {security}")
            try:
                # 只下载日线数据，减少数据量
                download_history_data(security, '1d', '20240101', '')
                print(f"  {security} 下载完成")
            except Exception as e:
                print(f"  {security} 下载失败: {str(e)}")
        
        print("调试版本：历史数据下载完成")
        
    except Exception as e:
        print(f"调试版本：下载历史数据时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
